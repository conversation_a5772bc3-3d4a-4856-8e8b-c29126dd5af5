#!/usr/bin/env python3
"""
NIFTY 50 Option Chain Analysis System
====================================

Complete analysis system for real NIFTY 50 option chain data from NSE.
Provides comprehensive analysis including PCR, Max Pain, OI analysis, etc.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import pytz
import logging
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from real_nse_fetcher import RealNSEOptionChainFetcher

class NiftyOptionChainAnalyzer:
    """
    Comprehensive NIFTY Option Chain Analyzer
    
    Analyzes real NSE option chain data with advanced metrics
    """
    
    def __init__(self):
        """Initialize the analyzer"""
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("NIFTY Option Chain Analyzer initialized")
    
    def analyze_option_chain(self, df: pd.DataFrame) -> Dict:
        """
        Comprehensive option chain analysis
        
        Args:
            df: Option chain DataFrame from NSE
            
        Returns:
            Dictionary with complete analysis results
        """
        if df.empty:
            self.logger.error("Cannot analyze empty DataFrame")
            return {}
        
        try:
            analysis = {}
            
            # Basic information
            analysis['basic_info'] = self._get_basic_info(df)
            
            # Market metrics
            analysis['market_metrics'] = self._calculate_market_metrics(df)
            
            # OI Analysis
            analysis['oi_analysis'] = self._analyze_open_interest(df)
            
            # Volume Analysis
            analysis['volume_analysis'] = self._analyze_volume(df)
            
            # Max Pain Analysis
            analysis['max_pain'] = self._calculate_max_pain(df)
            
            # Support/Resistance levels
            analysis['support_resistance'] = self._find_support_resistance(df)
            
            # ATM Analysis
            analysis['atm_analysis'] = self._analyze_atm_options(df)
            
            # Expiry Analysis
            analysis['expiry_analysis'] = self._analyze_expiries(df)
            
            # Greeks Analysis (simplified)
            analysis['greeks_analysis'] = self._analyze_greeks(df)
            
            self.logger.info("✅ Complete option chain analysis completed")
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ Error in analysis: {e}")
            return {}
    
    def _get_basic_info(self, df: pd.DataFrame) -> Dict:
        """Get basic information about the option chain"""
        try:
            underlying_price = df['underlying_value'].iloc[0]
            total_records = len(df)
            ce_count = len(df[df['option_type'] == 'CE'])
            pe_count = len(df[df['option_type'] == 'PE'])
            unique_strikes = len(df['strike_price'].unique())
            unique_expiries = len(df['expiry_date'].unique())
            
            return {
                'underlying_price': underlying_price,
                'total_records': total_records,
                'ce_count': ce_count,
                'pe_count': pe_count,
                'unique_strikes': unique_strikes,
                'unique_expiries': unique_expiries,
                'strike_range': {
                    'min': df['strike_price'].min(),
                    'max': df['strike_price'].max()
                },
                'expiry_dates': sorted(df['expiry_date'].unique()),
                'fetch_time': df['fetch_timestamp'].iloc[0]
            }
        except Exception as e:
            self.logger.error(f"Error in basic info: {e}")
            return {}
    
    def _calculate_market_metrics(self, df: pd.DataFrame) -> Dict:
        """Calculate key market metrics"""
        try:
            # Total OI and Volume
            total_ce_oi = df[df['option_type'] == 'CE']['open_interest'].sum()
            total_pe_oi = df[df['option_type'] == 'PE']['open_interest'].sum()
            total_ce_volume = df[df['option_type'] == 'CE']['volume'].sum()
            total_pe_volume = df[df['option_type'] == 'PE']['volume'].sum()
            
            # Put-Call Ratios
            pcr_oi = total_pe_oi / total_ce_oi if total_ce_oi > 0 else 0
            pcr_volume = total_pe_volume / total_ce_volume if total_ce_volume > 0 else 0
            
            # Average IV
            avg_ce_iv = df[df['option_type'] == 'CE']['implied_volatility'].mean()
            avg_pe_iv = df[df['option_type'] == 'PE']['implied_volatility'].mean()
            
            return {
                'total_oi': {
                    'ce': total_ce_oi,
                    'pe': total_pe_oi,
                    'total': total_ce_oi + total_pe_oi
                },
                'total_volume': {
                    'ce': total_ce_volume,
                    'pe': total_pe_volume,
                    'total': total_ce_volume + total_pe_volume
                },
                'pcr': {
                    'oi': pcr_oi,
                    'volume': pcr_volume
                },
                'implied_volatility': {
                    'ce_avg': avg_ce_iv,
                    'pe_avg': avg_pe_iv,
                    'overall_avg': (avg_ce_iv + avg_pe_iv) / 2
                }
            }
        except Exception as e:
            self.logger.error(f"Error in market metrics: {e}")
            return {}
    
    def _analyze_open_interest(self, df: pd.DataFrame) -> Dict:
        """Analyze Open Interest patterns"""
        try:
            # Top strikes by OI
            ce_top_oi = df[df['option_type'] == 'CE'].nlargest(10, 'open_interest')[
                ['strike_price', 'open_interest', 'last_price']].to_dict('records')
            
            pe_top_oi = df[df['option_type'] == 'PE'].nlargest(10, 'open_interest')[
                ['strike_price', 'open_interest', 'last_price']].to_dict('records')
            
            # OI changes
            ce_oi_changes = df[df['option_type'] == 'CE'].nlargest(10, 'oi_change')[
                ['strike_price', 'oi_change', 'open_interest']].to_dict('records')
            
            pe_oi_changes = df[df['option_type'] == 'PE'].nlargest(10, 'oi_change')[
                ['strike_price', 'oi_change', 'open_interest']].to_dict('records')
            
            # OI concentration
            total_ce_oi = df[df['option_type'] == 'CE']['open_interest'].sum()
            total_pe_oi = df[df['option_type'] == 'PE']['open_interest'].sum()
            
            # Top 5 strikes concentration
            top5_ce_oi = df[df['option_type'] == 'CE'].nlargest(5, 'open_interest')['open_interest'].sum()
            top5_pe_oi = df[df['option_type'] == 'PE'].nlargest(5, 'open_interest')['open_interest'].sum()
            
            ce_concentration = (top5_ce_oi / total_ce_oi * 100) if total_ce_oi > 0 else 0
            pe_concentration = (top5_pe_oi / total_pe_oi * 100) if total_pe_oi > 0 else 0
            
            return {
                'top_ce_oi': ce_top_oi,
                'top_pe_oi': pe_top_oi,
                'top_ce_oi_changes': ce_oi_changes,
                'top_pe_oi_changes': pe_oi_changes,
                'concentration': {
                    'ce_top5_percent': ce_concentration,
                    'pe_top5_percent': pe_concentration
                }
            }
        except Exception as e:
            self.logger.error(f"Error in OI analysis: {e}")
            return {}
    
    def _analyze_volume(self, df: pd.DataFrame) -> Dict:
        """Analyze trading volume patterns"""
        try:
            # Top strikes by volume
            ce_top_volume = df[df['option_type'] == 'CE'].nlargest(10, 'volume')[
                ['strike_price', 'volume', 'last_price']].to_dict('records')
            
            pe_top_volume = df[df['option_type'] == 'PE'].nlargest(10, 'volume')[
                ['strike_price', 'volume', 'last_price']].to_dict('records')
            
            # Volume vs OI ratio
            df_copy = df.copy()
            df_copy['volume_oi_ratio'] = df_copy['volume'] / (df_copy['open_interest'] + 1)  # +1 to avoid division by zero
            
            high_activity_ce = df_copy[df_copy['option_type'] == 'CE'].nlargest(10, 'volume_oi_ratio')[
                ['strike_price', 'volume', 'open_interest', 'volume_oi_ratio']].to_dict('records')
            
            high_activity_pe = df_copy[df_copy['option_type'] == 'PE'].nlargest(10, 'volume_oi_ratio')[
                ['strike_price', 'volume', 'open_interest', 'volume_oi_ratio']].to_dict('records')
            
            return {
                'top_ce_volume': ce_top_volume,
                'top_pe_volume': pe_top_volume,
                'high_activity_ce': high_activity_ce,
                'high_activity_pe': high_activity_pe
            }
        except Exception as e:
            self.logger.error(f"Error in volume analysis: {e}")
            return {}
    
    def _calculate_max_pain(self, df: pd.DataFrame) -> Dict:
        """Calculate Max Pain point"""
        try:
            strikes = sorted(df['strike_price'].unique())
            max_pain_data = []
            
            for strike in strikes:
                total_pain = 0
                
                # Pain from CE options (ITM calls)
                ce_options = df[(df['option_type'] == 'CE') & (df['strike_price'] < strike)]
                for _, option in ce_options.iterrows():
                    pain = (strike - option['strike_price']) * option['open_interest']
                    total_pain += pain
                
                # Pain from PE options (ITM puts)
                pe_options = df[(df['option_type'] == 'PE') & (df['strike_price'] > strike)]
                for _, option in pe_options.iterrows():
                    pain = (option['strike_price'] - strike) * option['open_interest']
                    total_pain += pain
                
                max_pain_data.append({
                    'strike': strike,
                    'total_pain': total_pain
                })
            
            # Find minimum pain (Max Pain point)
            max_pain_df = pd.DataFrame(max_pain_data)
            max_pain_strike = max_pain_df.loc[max_pain_df['total_pain'].idxmin(), 'strike']
            min_pain_value = max_pain_df['total_pain'].min()
            
            return {
                'max_pain_strike': max_pain_strike,
                'min_pain_value': min_pain_value,
                'pain_data': max_pain_data[:10]  # Top 10 for brevity
            }
        except Exception as e:
            self.logger.error(f"Error in Max Pain calculation: {e}")
            return {}
    
    def _find_support_resistance(self, df: pd.DataFrame) -> Dict:
        """Find potential support and resistance levels based on OI"""
        try:
            # High OI strikes often act as support/resistance
            ce_high_oi = df[df['option_type'] == 'CE'].nlargest(5, 'open_interest')['strike_price'].tolist()
            pe_high_oi = df[df['option_type'] == 'PE'].nlargest(5, 'open_interest')['strike_price'].tolist()
            
            underlying = df['underlying_value'].iloc[0]
            
            # Resistance levels (CE high OI above current price)
            resistance_levels = [strike for strike in ce_high_oi if strike > underlying]
            
            # Support levels (PE high OI below current price)
            support_levels = [strike for strike in pe_high_oi if strike < underlying]
            
            return {
                'resistance_levels': sorted(resistance_levels),
                'support_levels': sorted(support_levels, reverse=True),
                'ce_high_oi_strikes': ce_high_oi,
                'pe_high_oi_strikes': pe_high_oi
            }
        except Exception as e:
            self.logger.error(f"Error in support/resistance analysis: {e}")
            return {}
    
    def _analyze_atm_options(self, df: pd.DataFrame) -> Dict:
        """Analyze At-The-Money options"""
        try:
            underlying = df['underlying_value'].iloc[0]
            
            # Find ATM strike (closest to underlying)
            df_copy = df.copy()
            df_copy['distance_from_underlying'] = abs(df_copy['strike_price'] - underlying)
            atm_strike = df_copy.loc[df_copy['distance_from_underlying'].idxmin(), 'strike_price']
            
            # Get ATM option data
            atm_ce = df[(df['strike_price'] == atm_strike) & (df['option_type'] == 'CE')]
            atm_pe = df[(df['strike_price'] == atm_strike) & (df['option_type'] == 'PE')]
            
            atm_data = {}
            
            if not atm_ce.empty:
                ce_data = atm_ce.iloc[0]
                atm_data['ce'] = {
                    'last_price': ce_data['last_price'],
                    'change': ce_data['change'],
                    'percent_change': ce_data['percent_change'],
                    'volume': ce_data['volume'],
                    'open_interest': ce_data['open_interest'],
                    'implied_volatility': ce_data['implied_volatility']
                }
            
            if not atm_pe.empty:
                pe_data = atm_pe.iloc[0]
                atm_data['pe'] = {
                    'last_price': pe_data['last_price'],
                    'change': pe_data['change'],
                    'percent_change': pe_data['percent_change'],
                    'volume': pe_data['volume'],
                    'open_interest': pe_data['open_interest'],
                    'implied_volatility': pe_data['implied_volatility']
                }
            
            return {
                'atm_strike': atm_strike,
                'underlying_price': underlying,
                'atm_data': atm_data
            }
        except Exception as e:
            self.logger.error(f"Error in ATM analysis: {e}")
            return {}
    
    def _analyze_expiries(self, df: pd.DataFrame) -> Dict:
        """Analyze different expiry dates"""
        try:
            expiry_analysis = {}
            
            for expiry in df['expiry_date'].unique():
                expiry_df = df[df['expiry_date'] == expiry]
                
                ce_oi = expiry_df[expiry_df['option_type'] == 'CE']['open_interest'].sum()
                pe_oi = expiry_df[expiry_df['option_type'] == 'PE']['open_interest'].sum()
                ce_volume = expiry_df[expiry_df['option_type'] == 'CE']['volume'].sum()
                pe_volume = expiry_df[expiry_df['option_type'] == 'PE']['volume'].sum()
                
                expiry_analysis[expiry] = {
                    'total_records': len(expiry_df),
                    'ce_oi': ce_oi,
                    'pe_oi': pe_oi,
                    'ce_volume': ce_volume,
                    'pe_volume': pe_volume,
                    'pcr_oi': pe_oi / ce_oi if ce_oi > 0 else 0,
                    'pcr_volume': pe_volume / ce_volume if ce_volume > 0 else 0
                }
            
            return expiry_analysis
        except Exception as e:
            self.logger.error(f"Error in expiry analysis: {e}")
            return {}
    
    def _analyze_greeks(self, df: pd.DataFrame) -> Dict:
        """Simplified Greeks analysis"""
        try:
            # This is a simplified analysis since we don't have actual Greeks from NSE
            # We'll use IV and moneyness as proxies
            
            underlying = df['underlying_value'].iloc[0]
            
            # High IV options (potential high gamma)
            high_iv_ce = df[df['option_type'] == 'CE'].nlargest(5, 'implied_volatility')[
                ['strike_price', 'implied_volatility', 'last_price']].to_dict('records')
            
            high_iv_pe = df[df['option_type'] == 'PE'].nlargest(5, 'implied_volatility')[
                ['strike_price', 'implied_volatility', 'last_price']].to_dict('records')
            
            # ATM options (typically highest gamma)
            df_copy = df.copy()
            df_copy['distance_from_atm'] = abs(df_copy['strike_price'] - underlying)
            atm_options = df_copy.nsmallest(10, 'distance_from_atm')[
                ['strike_price', 'option_type', 'implied_volatility', 'volume']].to_dict('records')
            
            return {
                'high_iv_ce': high_iv_ce,
                'high_iv_pe': high_iv_pe,
                'atm_options': atm_options,
                'avg_iv_by_moneyness': {
                    'itm': df[df['moneyness'] == 'ITM']['implied_volatility'].mean(),
                    'atm': df[df['moneyness'] == 'ATM']['implied_volatility'].mean(),
                    'otm': df[df['moneyness'] == 'OTM']['implied_volatility'].mean()
                }
            }
        except Exception as e:
            self.logger.error(f"Error in Greeks analysis: {e}")
            return {}
    
    def print_analysis_report(self, analysis: Dict):
        """Print comprehensive analysis report"""
        if not analysis:
            print("❌ No analysis data to display")
            return
        
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE NIFTY 50 OPTION CHAIN ANALYSIS REPORT")
        print("="*80)
        
        # Basic Info
        if 'basic_info' in analysis:
            basic = analysis['basic_info']
            print(f"\n📋 BASIC INFORMATION:")
            print(f"   NIFTY Price: ₹{basic.get('underlying_price', 0):,.2f}")
            print(f"   Total Records: {basic.get('total_records', 0)}")
            print(f"   CE Options: {basic.get('ce_count', 0)}")
            print(f"   PE Options: {basic.get('pe_count', 0)}")
            print(f"   Unique Strikes: {basic.get('unique_strikes', 0)}")
            print(f"   Strike Range: ₹{basic.get('strike_range', {}).get('min', 0):,.0f} - ₹{basic.get('strike_range', {}).get('max', 0):,.0f}")
            print(f"   Expiry Dates: {', '.join(basic.get('expiry_dates', []))}")
        
        # Market Metrics
        if 'market_metrics' in analysis:
            metrics = analysis['market_metrics']
            print(f"\n📈 MARKET METRICS:")
            oi = metrics.get('total_oi', {})
            volume = metrics.get('total_volume', {})
            pcr = metrics.get('pcr', {})
            iv = metrics.get('implied_volatility', {})
            
            print(f"   Total OI: CE={oi.get('ce', 0):,}, PE={oi.get('pe', 0):,}, Total={oi.get('total', 0):,}")
            print(f"   Total Volume: CE={volume.get('ce', 0):,}, PE={volume.get('pe', 0):,}, Total={volume.get('total', 0):,}")
            print(f"   PCR (OI): {pcr.get('oi', 0):.3f}")
            print(f"   PCR (Volume): {pcr.get('volume', 0):.3f}")
            print(f"   Avg IV: CE={iv.get('ce_avg', 0):.2f}%, PE={iv.get('pe_avg', 0):.2f}%, Overall={iv.get('overall_avg', 0):.2f}%")
        
        # Max Pain
        if 'max_pain' in analysis:
            max_pain = analysis['max_pain']
            print(f"\n💰 MAX PAIN ANALYSIS:")
            print(f"   Max Pain Strike: ₹{max_pain.get('max_pain_strike', 0):,.0f}")
            print(f"   Total Pain Value: ₹{max_pain.get('min_pain_value', 0):,.0f}")
        
        # Top OI
        if 'oi_analysis' in analysis:
            oi_analysis = analysis['oi_analysis']
            print(f"\n🔥 TOP OPEN INTEREST:")
            
            print("   Top 5 CE Strikes by OI:")
            for i, strike in enumerate(oi_analysis.get('top_ce_oi', [])[:5], 1):
                print(f"     {i}. ₹{strike['strike_price']:,.0f}: OI={strike['open_interest']:,}, LTP=₹{strike['last_price']:.2f}")
            
            print("   Top 5 PE Strikes by OI:")
            for i, strike in enumerate(oi_analysis.get('top_pe_oi', [])[:5], 1):
                print(f"     {i}. ₹{strike['strike_price']:,.0f}: OI={strike['open_interest']:,}, LTP=₹{strike['last_price']:.2f}")
        
        # ATM Analysis
        if 'atm_analysis' in analysis:
            atm = analysis['atm_analysis']
            print(f"\n🎯 AT-THE-MONEY ANALYSIS:")
            print(f"   ATM Strike: ₹{atm.get('atm_strike', 0):,.0f}")
            
            if 'atm_data' in atm:
                atm_data = atm['atm_data']
                if 'ce' in atm_data:
                    ce = atm_data['ce']
                    print(f"   ATM CE: LTP=₹{ce.get('last_price', 0):.2f}, Change={ce.get('change', 0):+.2f} ({ce.get('percent_change', 0):+.2f}%), OI={ce.get('open_interest', 0):,}, Vol={ce.get('volume', 0):,}")
                
                if 'pe' in atm_data:
                    pe = atm_data['pe']
                    print(f"   ATM PE: LTP=₹{pe.get('last_price', 0):.2f}, Change={pe.get('change', 0):+.2f} ({pe.get('percent_change', 0):+.2f}%), OI={pe.get('open_interest', 0):,}, Vol={pe.get('volume', 0):,}")
        
        # Support/Resistance
        if 'support_resistance' in analysis:
            sr = analysis['support_resistance']
            print(f"\n🛡️ SUPPORT & RESISTANCE LEVELS:")
            resistance = sr.get('resistance_levels', [])
            support = sr.get('support_levels', [])
            
            if resistance:
                print(f"   Resistance Levels: {', '.join([f'₹{level:,.0f}' for level in resistance[:5]])}")
            if support:
                print(f"   Support Levels: {', '.join([f'₹{level:,.0f}' for level in support[:5]])}")
        
        print("\n" + "="*80)
        print("✅ Analysis Report Complete")
        print("="*80)

def main():
    """Main function to run complete NIFTY analysis"""
    print("🚀 NIFTY 50 OPTION CHAIN ANALYSIS SYSTEM")
    print("="*60)
    
    # Initialize fetcher and analyzer
    fetcher = RealNSEOptionChainFetcher()
    analyzer = NiftyOptionChainAnalyzer()
    
    # Check if we should wait for market
    if not fetcher.is_market_open():
        print("⏰ Market is currently closed")
        response = input("🤔 Wait for market to open? (y/n): ").lower()
        if response == 'y':
            fetcher.wait_for_market_open()
    
    # Fetch real NSE data
    print("\n📡 Fetching real NIFTY option chain data...")
    nse_data = fetcher.fetch_nse_option_chain("NIFTY")
    
    if nse_data:
        # Process data
        df = fetcher.process_nse_data_to_dataframe(nse_data)
        
        if not df.empty:
            print(f"✅ Successfully processed {len(df)} option records")
            
            # Save raw data
            filepath = fetcher.save_to_csv(df)
            
            # Perform comprehensive analysis
            print("\n🔍 Performing comprehensive analysis...")
            analysis = analyzer.analyze_option_chain(df)
            
            # Print analysis report
            analyzer.print_analysis_report(analysis)
            
            print(f"\n📁 Raw data saved to: {filepath}")
            
        else:
            print("❌ Failed to process NSE data")
    else:
        print("❌ Failed to fetch NSE data")
        print("💡 Try again during market hours (Mon-Fri, 9:15 AM - 3:30 PM IST)")

if __name__ == "__main__":
    main()
