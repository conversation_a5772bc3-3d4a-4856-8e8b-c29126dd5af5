#!/usr/bin/env python3
"""
Real NSE Option Chain Fetcher
============================

Fetches actual NIFTY 50 option chain data from NSE exactly as it appears
on the NSE website. No mock data - only real market data.

Market Hours: Monday to Friday, 9:15 AM - 3:30 PM IST
"""

import requests
import json
import pandas as pd
import time
from datetime import datetime, timedelta
import pytz
import logging
from typing import Dict, List, Optional
import os

class RealNSEOptionChainFetcher:
    """
    Real NSE Option Chain Data Fetcher
    
    Fetches actual option chain data from NSE India website
    """
    
    def __init__(self):
        """Initialize the NSE fetcher"""
        self.base_url = "https://www.nseindia.com"
        self.option_chain_url = f"{self.base_url}/api/option-chain-indices"
        
        # Indian timezone
        self.ist_tz = pytz.timezone('Asia/Kolkata')
        
        # Setup session with proper headers
        self.session = requests.Session()
        self._setup_session()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("Real NSE Option Chain Fetcher initialized")
    
    def _setup_session(self):
        """Setup session with proper headers to mimic browser"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(headers)
    
    def is_market_open(self) -> bool:
        """
        Check if Indian stock market is currently open
        
        Returns:
            True if market is open, False otherwise
        """
        now = datetime.now(self.ist_tz)
        
        # Check if it's a weekday (Monday=0, Sunday=6)
        if now.weekday() >= 5:  # Saturday or Sunday
            return False
        
        # Market hours: 9:15 AM to 3:30 PM IST
        market_open = now.replace(hour=9, minute=15, second=0, microsecond=0)
        market_close = now.replace(hour=15, minute=30, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    def get_next_market_open(self) -> datetime:
        """
        Get the next market opening time
        
        Returns:
            Next market opening datetime
        """
        now = datetime.now(self.ist_tz)
        
        # If it's before 9:15 AM today and it's a weekday
        if now.weekday() < 5 and now.hour < 9 or (now.hour == 9 and now.minute < 15):
            return now.replace(hour=9, minute=15, second=0, microsecond=0)
        
        # Otherwise, next Monday 9:15 AM
        days_ahead = 7 - now.weekday()  # Days until next Monday
        if now.weekday() == 6:  # If Sunday
            days_ahead = 1
        elif now.weekday() < 5:  # If weekday but after market hours
            days_ahead = 1
        
        next_open = now + timedelta(days=days_ahead)
        return next_open.replace(hour=9, minute=15, second=0, microsecond=0)
    
    def wait_for_market_open(self, max_wait_minutes: int = 60):
        """
        Wait for market to open (with timeout)
        
        Args:
            max_wait_minutes: Maximum minutes to wait
        """
        if self.is_market_open():
            self.logger.info("Market is already open!")
            return
        
        next_open = self.get_next_market_open()
        now = datetime.now(self.ist_tz)
        wait_time = (next_open - now).total_seconds()
        
        if wait_time > max_wait_minutes * 60:
            self.logger.warning(f"Market opens in {wait_time/3600:.1f} hours. Not waiting (max wait: {max_wait_minutes} minutes)")
            return
        
        self.logger.info(f"Market closed. Waiting for market to open at {next_open.strftime('%Y-%m-%d %H:%M:%S IST')}")
        self.logger.info(f"Wait time: {wait_time/60:.1f} minutes")
        
        while not self.is_market_open() and wait_time > 0:
            time.sleep(30)  # Check every 30 seconds
            wait_time -= 30
            if wait_time % 300 == 0:  # Log every 5 minutes
                self.logger.info(f"Still waiting... {wait_time/60:.1f} minutes remaining")
    
    def fetch_nse_option_chain(self, symbol: str = "NIFTY") -> Optional[Dict]:
        """
        Fetch real option chain data from NSE
        
        Args:
            symbol: Index symbol (NIFTY)
            
        Returns:
            Raw NSE option chain data or None if failed
        """
        try:
            self.logger.info(f"Fetching real NSE option chain for {symbol}...")
            
            # First, get the main page to establish session
            try:
                self.session.get(self.base_url, timeout=10)
                self.logger.info("NSE session established")
            except:
                self.logger.warning("Could not establish NSE session, continuing...")
            
            # Update headers for API call
            api_headers = {
                'Accept': '*/*',
                'Referer': 'https://www.nseindia.com/option-chain',
                'X-Requested-With': 'XMLHttpRequest'
            }
            self.session.headers.update(api_headers)
            
            # Make the API call
            params = {'symbol': symbol}
            response = self.session.get(
                self.option_chain_url,
                params=params,
                timeout=15
            )
            
            self.logger.info(f"NSE API response status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Validate the response
                    if self._validate_nse_response(data):
                        self.logger.info("✅ Successfully fetched real NSE option chain data")
                        return data
                    else:
                        self.logger.error("❌ Invalid NSE response structure")
                        return None
                        
                except json.JSONDecodeError as e:
                    self.logger.error(f"❌ Failed to parse NSE JSON response: {e}")
                    return None
            
            elif response.status_code == 401:
                self.logger.warning("❌ NSE returned 401 - Market likely closed or access restricted")
                return None
            
            else:
                self.logger.error(f"❌ NSE API failed with status {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error fetching NSE data: {e}")
            return None
    
    def _validate_nse_response(self, data: Dict) -> bool:
        """Validate NSE response structure"""
        try:
            if not isinstance(data, dict):
                return False
            
            records = data.get('records', {})
            if not isinstance(records, dict):
                return False
            
            # Check for required fields
            required_fields = ['underlyingValue', 'data', 'expiryDates']
            for field in required_fields:
                if field not in records:
                    self.logger.error(f"Missing required field: {field}")
                    return False
            
            option_data = records.get('data', [])
            if not isinstance(option_data, list) or len(option_data) == 0:
                self.logger.error("No option data found")
                return False
            
            self.logger.info(f"Validation passed: {len(option_data)} option records found")
            return True
            
        except Exception as e:
            self.logger.error(f"Validation error: {e}")
            return False
    
    def process_nse_data_to_dataframe(self, nse_data: Dict) -> pd.DataFrame:
        """
        Convert NSE option chain data to pandas DataFrame
        
        Args:
            nse_data: Raw NSE option chain data
            
        Returns:
            Processed DataFrame with all option data
        """
        try:
            records = nse_data.get('records', {})
            underlying_value = records.get('underlyingValue', 0)
            option_data = records.get('data', [])
            timestamp = datetime.now(self.ist_tz)
            
            processed_options = []
            
            for record in option_data:
                strike_price = record.get('strikePrice', 0)
                expiry_date = record.get('expiryDate', '')
                
                # Process CE (Call) data
                ce_data = record.get('CE', {})
                if ce_data:
                    ce_option = {
                        'strike_price': float(strike_price),
                        'expiry_date': expiry_date,
                        'option_type': 'CE',
                        'underlying_value': float(underlying_value),
                        'last_price': float(ce_data.get('lastPrice', 0)),
                        'change': float(ce_data.get('change', 0)),
                        'percent_change': float(ce_data.get('pChange', 0)),
                        'volume': int(ce_data.get('totalTradedVolume', 0)),
                        'open_interest': int(ce_data.get('openInterest', 0)),
                        'oi_change': int(ce_data.get('changeinOpenInterest', 0)),
                        'implied_volatility': float(ce_data.get('impliedVolatility', 0)),
                        'bid_price': float(ce_data.get('bidprice', 0)),
                        'ask_price': float(ce_data.get('askprice', 0)),
                        'bid_qty': int(ce_data.get('bidQty', 0)),
                        'ask_qty': int(ce_data.get('askQty', 0)),
                        'fetch_timestamp': timestamp,
                        'data_source': 'NSE_REAL'
                    }
                    processed_options.append(ce_option)
                
                # Process PE (Put) data
                pe_data = record.get('PE', {})
                if pe_data:
                    pe_option = {
                        'strike_price': float(strike_price),
                        'expiry_date': expiry_date,
                        'option_type': 'PE',
                        'underlying_value': float(underlying_value),
                        'last_price': float(pe_data.get('lastPrice', 0)),
                        'change': float(pe_data.get('change', 0)),
                        'percent_change': float(pe_data.get('pChange', 0)),
                        'volume': int(pe_data.get('totalTradedVolume', 0)),
                        'open_interest': int(pe_data.get('openInterest', 0)),
                        'oi_change': int(pe_data.get('changeinOpenInterest', 0)),
                        'implied_volatility': float(pe_data.get('impliedVolatility', 0)),
                        'bid_price': float(pe_data.get('bidprice', 0)),
                        'ask_price': float(pe_data.get('askprice', 0)),
                        'bid_qty': int(pe_data.get('bidQty', 0)),
                        'ask_qty': int(pe_data.get('askQty', 0)),
                        'fetch_timestamp': timestamp,
                        'data_source': 'NSE_REAL'
                    }
                    processed_options.append(pe_option)
            
            if not processed_options:
                self.logger.error("No options processed from NSE data")
                return pd.DataFrame()
            
            # Create DataFrame
            df = pd.DataFrame(processed_options)
            
            # Add calculated fields
            df['bid_ask_spread'] = df['ask_price'] - df['bid_price']
            df['moneyness'] = df.apply(lambda row: self._calculate_moneyness(
                row['strike_price'], row['underlying_value'], row['option_type']), axis=1)
            
            # Sort by expiry, strike, and option type
            df = df.sort_values(['expiry_date', 'strike_price', 'option_type'])
            
            self.logger.info(f"✅ Processed {len(df)} real option records from NSE")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ Error processing NSE data: {e}")
            return pd.DataFrame()
    
    def _calculate_moneyness(self, strike: float, underlying: float, option_type: str) -> str:
        """Calculate option moneyness"""
        if underlying == 0:
            return 'UNKNOWN'
        
        # ATM range (±0.5% of underlying)
        atm_range = underlying * 0.005
        
        if abs(strike - underlying) <= atm_range:
            return 'ATM'
        elif option_type == 'CE':
            return 'ITM' if strike < underlying else 'OTM'
        else:  # PE
            return 'ITM' if strike > underlying else 'OTM'
    
    def save_to_csv(self, df: pd.DataFrame, filename: Optional[str] = None) -> str:
        """
        Save DataFrame to CSV file
        
        Args:
            df: DataFrame to save
            filename: Optional custom filename
            
        Returns:
            Path to saved file
        """
        try:
            if df.empty:
                self.logger.warning("Cannot save empty DataFrame")
                return ""
            
            # Create data directory
            data_dir = "real_nse_data"
            os.makedirs(data_dir, exist_ok=True)
            
            # Generate filename if not provided
            if filename is None:
                timestamp = datetime.now(self.ist_tz).strftime('%Y%m%d_%H%M%S')
                filename = f"nifty_option_chain_real_{timestamp}.csv"
            
            filepath = os.path.join(data_dir, filename)
            
            # Save to CSV
            df.to_csv(filepath, index=False)
            
            self.logger.info(f"✅ Real NSE data saved to: {filepath}")
            self.logger.info(f"   Records: {len(df)}")
            self.logger.info(f"   File size: {os.path.getsize(filepath)/1024:.1f} KB")
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"❌ Error saving to CSV: {e}")
            return ""

def main():
    """Main function to fetch and save real NSE option chain data"""
    print("🚀 REAL NSE OPTION CHAIN FETCHER")
    print("="*50)
    
    fetcher = RealNSEOptionChainFetcher()
    
    # Check market status
    if not fetcher.is_market_open():
        print("⏰ Market is currently closed")
        print("📅 Indian stock market hours: Monday-Friday, 9:15 AM - 3:30 PM IST")
        
        next_open = fetcher.get_next_market_open()
        print(f"📅 Next market open: {next_open.strftime('%A, %B %d, %Y at %H:%M IST')}")
        
        # Ask user if they want to wait
        response = input("\n🤔 Do you want to wait for market to open? (y/n): ").lower()
        if response == 'y':
            fetcher.wait_for_market_open(max_wait_minutes=60)
        else:
            print("⚠️  Attempting to fetch data anyway (may return stale data)...")
    else:
        print("✅ Market is open! Fetching live data...")
    
    # Fetch real NSE data
    print("\n📡 Fetching real NIFTY option chain from NSE...")
    nse_data = fetcher.fetch_nse_option_chain("NIFTY")
    
    if nse_data:
        print("✅ Successfully fetched real NSE data!")
        
        # Process to DataFrame
        print("🔄 Processing data...")
        df = fetcher.process_nse_data_to_dataframe(nse_data)
        
        if not df.empty:
            print(f"✅ Processed {len(df)} option records")
            
            # Display summary
            underlying = df['underlying_value'].iloc[0]
            total_ce_oi = df[df['option_type'] == 'CE']['open_interest'].sum()
            total_pe_oi = df[df['option_type'] == 'PE']['open_interest'].sum()
            pcr = total_pe_oi / total_ce_oi if total_ce_oi > 0 else 0
            
            print(f"\n📊 REAL NSE DATA SUMMARY:")
            print(f"   NIFTY Price: ₹{underlying:,.2f}")
            print(f"   Total CE OI: {total_ce_oi:,}")
            print(f"   Total PE OI: {total_pe_oi:,}")
            print(f"   PCR (OI): {pcr:.3f}")
            
            # Save to CSV
            print("\n💾 Saving to CSV...")
            filepath = fetcher.save_to_csv(df)
            
            if filepath:
                print(f"✅ Data saved successfully!")
                print(f"📁 File: {filepath}")
                
                # Show sample data
                print(f"\n📋 Sample Data (first 5 records):")
                sample_cols = ['strike_price', 'option_type', 'last_price', 'open_interest', 'volume']
                print(df[sample_cols].head().to_string(index=False))
            
        else:
            print("❌ Failed to process NSE data")
    else:
        print("❌ Failed to fetch NSE data")
        print("💡 This could be due to:")
        print("   - Market is closed")
        print("   - Network connectivity issues")
        print("   - NSE server maintenance")
        print("   - Rate limiting")

if __name__ == "__main__":
    main()
