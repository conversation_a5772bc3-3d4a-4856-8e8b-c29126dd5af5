# NIFTY 50 Option Chain Data Fetcher

A comprehensive Python module for fetching, storing, and analyzing NIFTY 50 option chain data with support for both historical and live data collection.

## 🎯 Features

✅ **Dual Data Sources**: DhanHQ API (primary) + NSE India API (fallback)  
✅ **Historical Data**: Fetch past 2 trading days with configurable intervals  
✅ **Live Data Streaming**: Real-time option chain updates every N minutes  
✅ **CSV Storage**: Time-stamped files with organized directory structure  
✅ **Pandas Integration**: Clean data analysis and filtering capabilities  
✅ **IST Timezone**: Proper Indian market time alignment  
✅ **Comprehensive Metrics**: OI, IV, LTP, volume, change tracking  
✅ **Error Handling**: Rate limits, API failures, automatic reconnection  

## 📁 Project Structure

```
optionchain/
├── option_chain_agent.py      # Main entry point
├── README.md                  # This file
└── data/                      # Generated data directory
    └── option_chain_data/
        ├── historical/        # Historical snapshots
        └── live/             # Live data files

src/optionchain/
├── __init__.py               # Module initialization
├── fetcher.py               # Main OptionChainFetcher class
├── dhan_api.py              # DhanHQ API integration
├── nse_api.py               # NSE fallback API
├── data_processor.py        # Data normalization
├── storage.py               # CSV file management
└── analyzer.py              # Data analysis utilities
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Install required packages
pip install pandas numpy requests pytz

# Optional: Install DhanHQ library for enhanced features
pip install dhanhq websocket-client
```

### 2. Configure Credentials

Copy and edit the configuration file:

```bash
cp config/config.json.example config/config.json
```

Edit `config/config.json` and add your DhanHQ credentials:

```json
{
  "dhan_credentials": {
    "client_id": "YOUR_DHAN_CLIENT_ID_HERE",
    "access_token": "YOUR_DHAN_ACCESS_TOKEN_HERE"
  },
  "option_chain": {
    "symbol": "NIFTY",
    "exchange": "NSE",
    "fetch_interval_seconds": 300,
    "historical_days": 2,
    "historical_interval_minutes": 15,
    "atm_strikes_range": 5
  }
}
```

### 3. Basic Usage

```python
from option_chain_agent import OptionChainFetcher

# Initialize fetcher
fetcher = OptionChainFetcher(symbol="NIFTY", exchange="NSE")

# Fetch live data
df = fetcher.fetch_live_option_chain()
if df is not None:
    print(f"Fetched {len(df)} option records")
    
    # Display filtered chain (ATM ± 5 strikes)
    filtered_df = fetcher.display_filtered_chain(df, around_strike=5)
    
    # Save to CSV
    csv_path = fetcher.save_to_csv(df, mode="live")
    print(f"Data saved to: {csv_path}")
```

### 4. Historical Data

```python
# Fetch and store 2 days of historical data
success = fetcher.fetch_and_store_historical(days=2, interval_minutes=15)
if success:
    print("Historical data fetch completed")
```

### 5. Live Streaming

```python
def data_callback(df):
    print(f"Received {len(df)} records at {datetime.now()}")

# Start live streaming every 5 minutes
fetcher.start_live_fetch(interval_seconds=300, callback=data_callback)

# Stop streaming
fetcher.stop_live_fetch()
```

## 🖥️ Command Line Interface

Run the demo scripts:

```bash
cd optionchain

# Basic demo - fetch and display current option chain
python option_chain_agent.py --demo basic

# Historical data demo
python option_chain_agent.py --demo historical

# Live streaming demo (runs for 5 minutes)
python option_chain_agent.py --demo live
```

## 📊 Data Format

The option chain data is normalized into a consistent pandas DataFrame format:

| Column | Description | Type |
|--------|-------------|------|
| `strike_price` | Option strike price | float |
| `expiry_date` | Expiry date (DD-MMM-YYYY) | string |
| `option_type` | CE (Call) or PE (Put) | string |
| `ltp` | Last Traded Price | float |
| `change` | Price change from previous close | float |
| `change_percent` | Percentage change | float |
| `volume` | Trading volume | int |
| `oi` | Open Interest | int |
| `oi_change` | Change in Open Interest | int |
| `iv` | Implied Volatility | float |
| `bid` | Bid price | float |
| `ask` | Ask price | float |
| `underlying_price` | Current NIFTY price | float |
| `data_source` | DhanHQ or NSE | string |
| `fetch_timestamp` | Data fetch time (IST) | datetime |

## 🔧 Advanced Usage

### Custom Analysis

```python
from optionchain.analyzer import OptionChainAnalyzer

analyzer = OptionChainAnalyzer()

# Get option chain summary
summary = analyzer.get_option_summary(df)
print(f"Total OI: {summary['total_oi']:,}")
print(f"PCR (OI): {summary['pcr_oi']:.3f}")

# Calculate Max Pain
max_pain = analyzer.calculate_max_pain(df)
print(f"Max Pain: ₹{max_pain:,.0f}")

# Get top OI strikes
top_oi = analyzer.get_top_oi_strikes(df, top_n=5)
```

### Data Storage Management

```python
from optionchain.storage import OptionChainStorage

storage = OptionChainStorage(data_dir="data", symbol="NIFTY")

# Load latest live data
latest_df = storage.load_latest_live_data()

# Load historical data for specific date
hist_df = storage.load_historical_data(date="20250601")

# Get available dates
dates = storage.get_available_dates("historical")

# Cleanup old files (keep last 30 days)
storage.cleanup_old_files(days_to_keep=30)
```

## 📈 Analysis Features

### Option Chain Summary
- Total options count (CE/PE breakdown)
- Open Interest totals and Put-Call Ratio
- Volume analysis
- Strike price ranges
- Max Pain calculation

### Filtering Capabilities
- ATM ± N strikes filtering
- Expiry date filtering
- Moneyness classification (ITM/ATM/OTM)
- Top strikes by OI/Volume

### Calculated Metrics
- Intrinsic value
- Time value
- Days to expiry
- Bid-ask spread
- Delta approximation

## ⚠️ Important Notes

### Market Hours
- NSE options trade: 9:15 AM - 3:30 PM IST (Monday-Friday)
- Data fetching outside market hours may return stale data

### API Limitations
- **DhanHQ**: Requires valid credentials and active subscription
- **NSE**: Public API with rate limits (2-second intervals recommended)
- Both APIs may have usage restrictions during high volatility

### Data Accuracy
- Live data has ~1-2 second delay
- Historical data simulation is currently a placeholder
- Always verify critical trading decisions with official sources

## 🛠️ Troubleshooting

### Common Issues

1. **"No data fetched"**
   - Check market hours (9:15 AM - 3:30 PM IST)
   - Verify internet connection
   - Check API credentials

2. **"DhanHQ API not available"**
   - Install dhanhq library: `pip install dhanhq`
   - Verify credentials in config.json
   - Check DhanHQ account status

3. **"Rate limit exceeded"**
   - Increase fetch intervals
   - Wait before retrying
   - Check API usage limits

### Debug Mode

Enable debug logging for detailed information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 License

This project is part of the NIFTY 50 EMA Crossover Trading System.

## 🤝 Contributing

1. Follow the existing code structure
2. Add proper error handling and logging
3. Include unit tests for new features
4. Update documentation

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Verify configuration settings
4. Test with basic demo first
