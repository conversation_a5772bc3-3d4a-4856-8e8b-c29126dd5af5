#!/usr/bin/env python3
"""
NIFTY Option Chain Agent - Main Entry Point
===========================================

This is the main entry point for the NIFTY 50 Option Chain data fetcher.
It provides a simple interface to fetch, store, and analyze option chain data.

Usage Example:
    from option_chain_agent import OptionChainFetcher

    fetcher = OptionChainFetcher(symbol="NIFTY", exchange="NSE")

    # Historical: Fetch and save 2-day option chain
    fetcher.fetch_and_store_historical(days=2, interval_minutes=15)

    # Live: Fetch live every 5 minutes
    fetcher.start_live_fetch(interval_seconds=300)

Author: NiftyOptionChainAgent
Date: 2025
"""

import sys
import os
import logging
import json
from datetime import datetime
from typing import Dict, Optional

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from optionchain.fetcher import OptionChainFetcher
from optionchain.analyzer import Option<PERSON>hainAnalyzer
from optionchain.storage import OptionChainStorage


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """
    Setup logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        
    Returns:
        Configured logger
    """
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'option_chain_{datetime.now().strftime("%Y%m%d")}.log')
        ]
    )
    
    return logging.getLogger(__name__)


def load_config(config_path: str = "../config/config.json") -> Dict:
    """
    Load configuration from JSON file
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    try:
        config_file = os.path.join(os.path.dirname(__file__), config_path)
        
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
            print(f"✅ Configuration loaded from {config_file}")
            return config
        else:
            print(f"⚠️  Configuration file not found: {config_file}")
            print("Using default configuration...")
            return get_default_config()
            
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        print("Using default configuration...")
        return get_default_config()


def get_default_config() -> Dict:
    """
    Get default configuration
    
    Returns:
        Default configuration dictionary
    """
    return {
        "dhan_credentials": {
            "client_id": "",
            "access_token": ""
        },
        "data_directory": "data",
        "option_chain": {
            "symbol": "NIFTY",
            "exchange": "NSE",
            "fetch_interval_seconds": 300,
            "historical_days": 2,
            "historical_interval_minutes": 15,
            "atm_strikes_range": 5
        },
        "logging": {
            "level": "INFO"
        }
    }


def demo_basic_usage():
    """
    Demonstrate basic usage of the Option Chain Fetcher
    """
    print("\n" + "="*60)
    print("NIFTY 50 OPTION CHAIN AGENT - BASIC DEMO")
    print("="*60)
    
    # Load configuration
    config = load_config()
    
    # Setup logging
    logger = setup_logging(config.get('logging', {}).get('level', 'INFO'))
    
    # Initialize fetcher
    print("\n📊 Initializing Option Chain Fetcher...")
    fetcher = OptionChainFetcher(
        symbol=config.get('option_chain', {}).get('symbol', 'NIFTY'),
        exchange=config.get('option_chain', {}).get('exchange', 'NSE'),
        config=config
    )
    
    # Fetch live data
    print("\n📡 Fetching live option chain data...")
    live_data = fetcher.fetch_live_option_chain()
    
    if live_data is not None and not live_data.empty:
        print(f"✅ Successfully fetched {len(live_data)} option records")
        
        # Save to CSV
        print("\n💾 Saving data to CSV...")
        csv_path = fetcher.save_to_csv(live_data, mode="live")
        if csv_path:
            print(f"✅ Data saved to: {csv_path}")
        
        # Display filtered chain
        print("\n📈 Displaying filtered option chain (ATM ± 5 strikes)...")
        filtered_data = fetcher.display_filtered_chain(live_data, around_strike=5)
        
        if not filtered_data.empty:
            # Format for display
            analyzer = OptionChainAnalyzer()
            display_df = analyzer.format_display_table(filtered_data)
            
            print("\nFiltered Option Chain:")
            print(display_df.to_string(index=False))
            
            # Show top OI strikes
            top_oi = analyzer.get_top_oi_strikes(filtered_data, top_n=3)
            if top_oi:
                print(f"\n🔥 Top 3 CE strikes by OI:")
                for strike in top_oi.get('top_ce_oi', []):
                    print(f"  Strike {strike['strike_price']}: OI={strike['oi']:,}, LTP=₹{strike['ltp']}")
                
                print(f"\n🔥 Top 3 PE strikes by OI:")
                for strike in top_oi.get('top_pe_oi', []):
                    print(f"  Strike {strike['strike_price']}: OI={strike['oi']:,}, LTP=₹{strike['ltp']}")
        
    else:
        print("❌ Failed to fetch option chain data")
        print("This might be due to:")
        print("  - Missing DhanHQ credentials")
        print("  - Network connectivity issues")
        print("  - API rate limits")
        print("  - Market hours (NSE options trade 9:15 AM - 3:30 PM IST)")
    
    # Show statistics
    print(f"\n📊 Fetcher Statistics:")
    stats = fetcher.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n" + "="*60)
    print("Demo completed!")
    print("="*60)


def demo_historical_data():
    """
    Demonstrate historical data fetching
    """
    print("\n" + "="*60)
    print("HISTORICAL DATA DEMO")
    print("="*60)
    
    config = load_config()
    logger = setup_logging(config.get('logging', {}).get('level', 'INFO'))
    
    fetcher = OptionChainFetcher(
        symbol=config.get('option_chain', {}).get('symbol', 'NIFTY'),
        exchange=config.get('option_chain', {}).get('exchange', 'NSE'),
        config=config
    )
    
    print("\n📅 Fetching historical data (past 2 days)...")
    success = fetcher.fetch_and_store_historical(days=2, interval_minutes=15)
    
    if success:
        print("✅ Historical data fetch completed")
    else:
        print("⚠️  Historical data fetch completed with warnings")
        print("Note: Historical data simulation is currently a placeholder")
        print("In a production environment, this would fetch actual historical snapshots")


def demo_live_streaming():
    """
    Demonstrate live data streaming
    """
    print("\n" + "="*60)
    print("LIVE STREAMING DEMO")
    print("="*60)
    
    config = load_config()
    logger = setup_logging(config.get('logging', {}).get('level', 'INFO'))
    
    fetcher = OptionChainFetcher(
        symbol=config.get('option_chain', {}).get('symbol', 'NIFTY'),
        exchange=config.get('option_chain', {}).get('exchange', 'NSE'),
        config=config
    )
    
    def data_callback(df):
        """Callback function for live data"""
        print(f"📊 Live data received: {len(df)} records at {datetime.now()}")
        
        # Quick analysis
        analyzer = OptionChainAnalyzer()
        summary = analyzer.get_option_summary(df)
        print(f"   Underlying: ₹{summary.get('underlying_price', 0):,.2f}")
        print(f"   Total OI: {summary.get('total_oi', 0):,}")
        print(f"   PCR (OI): {summary.get('pcr_oi', 0):.3f}")
    
    print("\n📡 Starting live data streaming (every 60 seconds)...")
    print("Press Ctrl+C to stop...")
    
    try:
        # Start live fetch with 60-second intervals for demo
        success = fetcher.start_live_fetch(interval_seconds=60, callback=data_callback)
        
        if success:
            print("✅ Live streaming started successfully")
            
            # Keep running for demo (in practice, this would run indefinitely)
            import time
            time.sleep(300)  # Run for 5 minutes
            
        else:
            print("❌ Failed to start live streaming")
    
    except KeyboardInterrupt:
        print("\n⏹️  Stopping live streaming...")
    
    finally:
        fetcher.stop_live_fetch()
        print("✅ Live streaming stopped")


if __name__ == "__main__":
    """
    Main entry point - run different demos based on command line arguments
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="NIFTY 50 Option Chain Agent")
    parser.add_argument('--demo', choices=['basic', 'historical', 'live'], 
                       default='basic', help='Demo type to run')
    parser.add_argument('--config', default='../config/config.json',
                       help='Path to configuration file')
    
    args = parser.parse_args()
    
    try:
        if args.demo == 'basic':
            demo_basic_usage()
        elif args.demo == 'historical':
            demo_historical_data()
        elif args.demo == 'live':
            demo_live_streaming()
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
